'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { CheckCircle, Download, AlertCircle, Loader } from 'lucide-react';

interface ConversionProgressProps {
  isVisible: boolean;
  onClose: () => void;
  videoUrl: string;
  options: {
    bitrate: number;
    durationFactor: number;
  };
}

interface ProgressStep {
  id: string;
  label: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress?: number;
}

export default function ConversionProgress({ 
  isVisible, 
  onClose, 
  videoUrl, 
  options 
}: ConversionProgressProps) {
  const [steps, setSteps] = useState<ProgressStep[]>([
    { id: 'fetch', label: 'Fetching video information', status: 'pending' },
    { id: 'download', label: 'Downloading video', status: 'pending', progress: 0 },
    { id: 'convert', label: 'Converting to MP3', status: 'pending', progress: 0 },
    { id: 'upload', label: 'Preparing download', status: 'pending', progress: 0 },
  ]);

  const [currentStep, setCurrentStep] = useState(0);
  const [overallProgress, setOverallProgress] = useState(0);
  const [estimatedTime, setEstimatedTime] = useState('Calculating...');
  const [downloadUrl, setDownloadUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!isVisible) return;

    // Simulate conversion process
    const simulateConversion = async () => {
      try {
        // Step 1: Fetch video info
        setSteps(prev => prev.map((step, index) => 
          index === 0 ? { ...step, status: 'processing' } : step
        ));
        setEstimatedTime('2-3 minutes');
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        setSteps(prev => prev.map((step, index) => 
          index === 0 ? { ...step, status: 'completed' } : step
        ));
        setCurrentStep(1);
        setOverallProgress(25);

        // Step 2: Download video
        setSteps(prev => prev.map((step, index) => 
          index === 1 ? { ...step, status: 'processing' } : step
        ));
        setEstimatedTime('1-2 minutes');
        
        for (let i = 0; i <= 100; i += 10) {
          await new Promise(resolve => setTimeout(resolve, 200));
          setSteps(prev => prev.map((step, index) => 
            index === 1 ? { ...step, progress: i } : step
          ));
          setOverallProgress(25 + (i * 0.25));
        }
        
        setSteps(prev => prev.map((step, index) => 
          index === 1 ? { ...step, status: 'completed' } : step
        ));
        setCurrentStep(2);

        // Step 3: Convert to MP3
        setSteps(prev => prev.map((step, index) => 
          index === 2 ? { ...step, status: 'processing' } : step
        ));
        setEstimatedTime('30-60 seconds');
        
        for (let i = 0; i <= 100; i += 15) {
          await new Promise(resolve => setTimeout(resolve, 150));
          setSteps(prev => prev.map((step, index) => 
            index === 2 ? { ...step, progress: i } : step
          ));
          setOverallProgress(50 + (i * 0.25));
        }
        
        setSteps(prev => prev.map((step, index) => 
          index === 2 ? { ...step, status: 'completed' } : step
        ));
        setCurrentStep(3);

        // Step 4: Upload/Prepare
        setSteps(prev => prev.map((step, index) => 
          index === 3 ? { ...step, status: 'processing' } : step
        ));
        setEstimatedTime('10-20 seconds');
        
        for (let i = 0; i <= 100; i += 20) {
          await new Promise(resolve => setTimeout(resolve, 100));
          setSteps(prev => prev.map((step, index) => 
            index === 3 ? { ...step, progress: i } : step
          ));
          setOverallProgress(75 + (i * 0.25));
        }
        
        setSteps(prev => prev.map((step, index) => 
          index === 3 ? { ...step, status: 'completed' } : step
        ));
        setOverallProgress(100);
        setEstimatedTime('Completed!');
        setDownloadUrl('https://example.com/download.mp3'); // Mock download URL

      } catch (err) {
        setError('Conversion failed. Please try again.');
        setSteps(prev => prev.map((step, index) => 
          index === currentStep ? { ...step, status: 'error' } : step
        ));
      }
    };

    simulateConversion();
  }, [isVisible, currentStep]);

  const getStepIcon = (step: ProgressStep) => {
    switch (step.status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'processing':
        return <Loader className="w-5 h-5 text-blue-500 animate-spin" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <div className="w-5 h-5 rounded-full border-2 border-gray-300" />;
    }
  };

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-white rounded-2xl shadow-2xl max-w-md w-full p-6"
      >
        {/* Header */}
        <div className="text-center mb-6">
          <h3 className="text-2xl font-bold text-gray-900 mb-2">Converting Video</h3>
          <p className="text-gray-600 text-sm">Quality: {options.bitrate}kbps • Speed: {options.durationFactor}x</p>
        </div>

        {/* Overall Progress */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Overall Progress</span>
            <span className="text-sm text-gray-600">{Math.round(overallProgress)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <motion.div
              className="bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${overallProgress}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
          <p className="text-sm text-gray-600 mt-2">Estimated time: {estimatedTime}</p>
        </div>

        {/* Steps */}
        <div className="space-y-4 mb-6">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center space-x-3">
              {getStepIcon(step)}
              <div className="flex-1">
                <p className={`text-sm font-medium ${
                  step.status === 'completed' ? 'text-green-700' :
                  step.status === 'processing' ? 'text-blue-700' :
                  step.status === 'error' ? 'text-red-700' : 'text-gray-500'
                }`}>
                  {step.label}
                </p>
                {step.status === 'processing' && step.progress !== undefined && (
                  <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
                    <motion.div
                      className="bg-blue-500 h-1 rounded-full"
                      initial={{ width: 0 }}
                      animate={{ width: `${step.progress}%` }}
                      transition={{ duration: 0.3 }}
                    />
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex space-x-3">
          {downloadUrl ? (
            <>
              <a
                href={downloadUrl}
                download
                className="flex-1 bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors flex items-center justify-center"
              >
                <Download className="w-5 h-5 mr-2" />
                Download MP3
              </a>
              <button
                onClick={onClose}
                className="px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Close
              </button>
            </>
          ) : (
            <button
              onClick={onClose}
              className="w-full py-3 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
              disabled={!error && overallProgress < 100}
            >
              {error ? 'Close' : 'Cancel'}
            </button>
          )}
        </div>
      </motion.div>
    </motion.div>
  );
}
