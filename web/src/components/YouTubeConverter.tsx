'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Download, Music, Settings, Zap, Clock, TrendingUp } from 'lucide-react';
import Link from 'next/link';
import Button from './ui/Button';
import ConversionProgress from './ConversionProgress';
import { isValidYouTubeUrl } from '@/lib/utils';
import { ConversionOptions } from '@/types';

export default function YouTubeConverter() {
  const [url, setUrl] = useState('');
  const [options, setOptions] = useState<ConversionOptions>({
    bitrate: 192,
    durationFactor: 1.0
  });
  const [isConverting, setIsConverting] = useState(false);
  const [showOptions, setShowOptions] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [showProgress, setShowProgress] = useState(false);

  const handleConvert = async () => {
    if (!isValidYouTubeUrl(url)) {
      alert('Please enter a valid YouTube URL');
      return;
    }

    setIsConverting(true);
    setShowProgress(true);
  };

  const handleProgressClose = () => {
    setShowProgress(false);
    setIsConverting(false);
  };

  const bitrateOptions = [
    { value: 128, label: '128 kbps (Good)' },
    { value: 192, label: '192 kbps (High)' },
    { value: 320, label: '320 kbps (Best)' }
  ];

  const durationOptions = [
    { value: 1.0, label: '1.0x (Normal)' },
    { value: 1.5, label: '1.5x (Faster)' },
    { value: 2.0, label: '2.0x (Fastest)' }
  ];

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center mb-12"
      >
        <div className="flex items-center justify-center mb-4">
          <Music className="w-12 h-12 text-red-600 mr-3" />
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
            YouTube to MP3
          </h1>
        </div>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Convert YouTube videos to high-quality MP3 audio files. 
          Fast, free, and easy to use.
        </p>
      </motion.div>

      {/* Main Converter */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
        className="bg-white rounded-2xl shadow-2xl p-8 mb-8"
      >
        {/* URL Input with Rainbow Border Effect */}
        <div className="relative mb-6">
          <motion.div
            className={`absolute inset-0 rounded-xl bg-gradient-to-r from-red-500 via-yellow-500 via-green-500 via-blue-500 to-purple-500 opacity-0 transition-opacity duration-300 ${
              isHovered ? 'opacity-100' : 'opacity-0'
            }`}
            style={{
              background: isHovered 
                ? 'linear-gradient(90deg, #ef4444, #f59e0b, #10b981, #3b82f6, #8b5cf6, #ef4444)'
                : 'transparent',
              backgroundSize: '200% 100%',
              animation: isHovered ? 'rainbow 2s linear infinite' : 'none'
            }}
          />
          <div className="relative bg-white rounded-xl p-1">
            <input
              type="text"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="Paste YouTube URL here..."
              className="w-full px-6 py-4 text-lg border-2 border-gray-200 rounded-lg focus:border-red-500 focus:outline-none transition-colors"
            />
          </div>
        </div>

        {/* Options Toggle */}
        <div className="flex items-center justify-between mb-6">
          <button
            onClick={() => setShowOptions(!showOptions)}
            className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
          >
            <Settings className="w-5 h-5 mr-2" />
            Conversion Options
            <motion.div
              animate={{ rotate: showOptions ? 180 : 0 }}
              transition={{ duration: 0.2 }}
              className="ml-2"
            >
              ▼
            </motion.div>
          </button>
        </div>

        {/* Options Panel */}
        <motion.div
          initial={false}
          animate={{ height: showOptions ? 'auto' : 0, opacity: showOptions ? 1 : 0 }}
          transition={{ duration: 0.3 }}
          className="overflow-hidden"
        >
          <div className="grid md:grid-cols-2 gap-6 mb-6 p-4 bg-gray-50 rounded-lg">
            {/* Bitrate Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Audio Quality
              </label>
              <select
                value={options.bitrate}
                onChange={(e) => setOptions({ ...options, bitrate: Number(e.target.value) })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-red-500 focus:outline-none"
              >
                {bitrateOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Duration Factor */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Playback Speed
              </label>
              <select
                value={options.durationFactor}
                onChange={(e) => setOptions({ ...options, durationFactor: Number(e.target.value) })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-red-500 focus:outline-none"
              >
                {durationOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </motion.div>

        {/* Convert Button */}
        <div className="text-center">
          <Button
            onClick={handleConvert}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            isLoading={isConverting}
            size="lg"
            className="px-12 py-4 text-xl"
            disabled={!url.trim()}
          >
            {isConverting ? (
              <>
                <Zap className="w-6 h-6 mr-2 animate-pulse" />
                Converting...
              </>
            ) : (
              <>
                <Download className="w-6 h-6 mr-2" />
                Convert to MP3
              </>
            )}
          </Button>
        </div>
      </motion.div>

      {/* Features */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="grid md:grid-cols-3 gap-6 text-center"
      >
        <div className="p-6 bg-white rounded-xl shadow-lg">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Zap className="w-8 h-8 text-red-600" />
          </div>
          <h3 className="text-xl font-semibold mb-2">Lightning Fast</h3>
          <p className="text-gray-600">Convert videos in seconds with our optimized servers</p>
        </div>

        <div className="p-6 bg-white rounded-xl shadow-lg">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Music className="w-8 h-8 text-blue-600" />
          </div>
          <h3 className="text-xl font-semibold mb-2">High Quality</h3>
          <p className="text-gray-600">Multiple bitrate options for the best audio quality</p>
        </div>

        <div className="p-6 bg-white rounded-xl shadow-lg">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Download className="w-8 h-8 text-green-600" />
          </div>
          <h3 className="text-xl font-semibold mb-2">Free Forever</h3>
          <p className="text-gray-600">No registration required, completely free to use</p>
        </div>
      </motion.div>

      {/* Quick Links */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.6 }}
        className="grid md:grid-cols-2 gap-6 mt-8"
      >
        <Link
          href="/history"
          className="group bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
        >
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center group-hover:bg-blue-200 transition-colors">
              <Clock className="w-6 h-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-semibold ml-4 text-gray-900">View History</h3>
          </div>
          <p className="text-gray-600">Track your conversion history and re-download files</p>
        </Link>

        <Link
          href="/popular"
          className="group bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
        >
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center group-hover:bg-orange-200 transition-colors">
              <TrendingUp className="w-6 h-6 text-orange-600" />
            </div>
            <h3 className="text-xl font-semibold ml-4 text-gray-900">Popular Today</h3>
          </div>
          <p className="text-gray-600">Discover today's most converted YouTube videos</p>
        </Link>
      </motion.div>

      {/* Conversion Progress Modal */}
      <ConversionProgress
        isVisible={showProgress}
        onClose={handleProgressClose}
        videoUrl={url}
        options={options}
      />
    </div>
  );
}
