// Conversion related types
export interface ConversionOptions {
  bitrate: number;
  durationFactor: number;
}

export interface ConversionRequest {
  youtubeUrl: string;
  bitrate: number;
  durationFactor: number;
}

export interface ConversionRecord {
  id: string;
  videoTitle: string;
  youtubeUrl: string;
  mp3Url?: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  bitrate: number;
  durationFactor: number;
  createdAt: string;
  completedAt?: string;
  progress?: number;
  estimatedTime?: string;
  error?: string;
}

// Popular videos types
export interface PopularVideo {
  id: string;
  videoTitle: string;
  youtubeUrl: string;
  thumbnailUrl: string;
  downloadCount: number;
  dailyCount: number;
  lastUpdated: string;
}

// API response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: string;
}

export interface ConversionProgressStep {
  id: string;
  label: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress?: number;
}

// User types (for future implementation)
export interface User {
  id: string;
  email: string;
  createdAt: string;
}

// Browser extension types (for future implementation)
export interface ExtensionMessage {
  type: 'CONVERT_VIDEO' | 'GET_STATUS' | 'DOWNLOAD_READY';
  payload: any;
}

export interface VideoInfo {
  id: string;
  title: string;
  duration: string;
  thumbnail: string;
  url: string;
}
