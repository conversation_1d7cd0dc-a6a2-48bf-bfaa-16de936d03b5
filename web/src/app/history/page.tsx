'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Clock, Download, CheckCircle, XCircle, Music } from 'lucide-react';
import Link from 'next/link';

interface ConversionRecord {
  id: string;
  videoTitle: string;
  youtubeUrl: string;
  mp3Url?: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  bitrate: number;
  durationFactor: number;
  createdAt: string;
  completedAt?: string;
}

export default function HistoryPage() {
  const [history, setHistory] = useState<ConversionRecord[]>([]);

  useEffect(() => {
    // Mock data for demonstration
    const mockHistory: ConversionRecord[] = [
      {
        id: '1',
        videoTitle: 'Amazing Song - Official Music Video',
        youtubeUrl: 'https://youtube.com/watch?v=example1',
        mp3Url: 'https://example.com/download1.mp3',
        status: 'completed',
        bitrate: 320,
        durationFactor: 1.0,
        createdAt: '2024-01-15T10:30:00Z',
        completedAt: '2024-01-15T10:31:00Z'
      },
      {
        id: '2',
        videoTitle: 'Podcast Episode #123',
        youtubeUrl: 'https://youtube.com/watch?v=example2',
        status: 'processing',
        bitrate: 192,
        durationFactor: 1.5,
        createdAt: '2024-01-15T09:15:00Z'
      },
      {
        id: '3',
        videoTitle: 'Tutorial: How to Code',
        youtubeUrl: 'https://youtube.com/watch?v=example3',
        status: 'failed',
        bitrate: 128,
        durationFactor: 2.0,
        createdAt: '2024-01-14T16:45:00Z'
      }
    ];
    setHistory(mockHistory);
  }, []);

  const getStatusIcon = (status: ConversionRecord['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'processing':
        return <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />;
      case 'failed':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-yellow-500" />;
    }
  };

  const getStatusText = (status: ConversionRecord['status']) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'processing':
        return 'Processing';
      case 'failed':
        return 'Failed';
      default:
        return 'Pending';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes} minutes ago`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)} hours ago`;
    } else {
      return `${Math.floor(diffInMinutes / 1440)} days ago`;
    }
  };

  return (
    <div className="min-h-screen py-8">
      <div className="max-w-4xl mx-auto p-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8"
        >
          <div className="flex items-center justify-center mb-4">
            <Clock className="w-10 h-10 text-blue-600 mr-3" />
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900">
              Conversion History
            </h1>
          </div>
          <p className="text-lg text-gray-600">
            Track your YouTube to MP3 conversions
          </p>
        </motion.div>

        {/* Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="mb-8"
        >
          <Link
            href="/"
            className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
          >
            ← Back to Converter
          </Link>
        </motion.div>

        {/* History List */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-white rounded-2xl shadow-lg overflow-hidden"
        >
          {history.length === 0 ? (
            <div className="p-12 text-center">
              <Music className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-600 mb-2">No conversions yet</h3>
              <p className="text-gray-500">Start converting YouTube videos to see your history here.</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {history.map((record, index) => (
                <motion.div
                  key={record.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  className="p-6 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center mb-2">
                        {getStatusIcon(record.status)}
                        <span className="ml-2 text-sm font-medium text-gray-600">
                          {getStatusText(record.status)}
                        </span>
                        <span className="ml-4 text-sm text-gray-500">
                          {formatDate(record.createdAt)}
                        </span>
                      </div>
                      
                      <h3 className="text-lg font-semibold text-gray-900 mb-1 truncate">
                        {record.videoTitle}
                      </h3>
                      
                      <div className="flex flex-wrap gap-4 text-sm text-gray-600 mb-2">
                        <span>Quality: {record.bitrate}kbps</span>
                        <span>Speed: {record.durationFactor}x</span>
                      </div>
                      
                      <a
                        href={record.youtubeUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm text-blue-600 hover:text-blue-800 truncate block"
                      >
                        {record.youtubeUrl}
                      </a>
                    </div>
                    
                    <div className="ml-4 flex-shrink-0">
                      {record.status === 'completed' && record.mp3Url && (
                        <a
                          href={record.mp3Url}
                          download
                          className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                        >
                          <Download className="w-4 h-4 mr-2" />
                          Download
                        </a>
                      )}
                      {record.status === 'failed' && (
                        <button className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                          Retry
                        </button>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
}
