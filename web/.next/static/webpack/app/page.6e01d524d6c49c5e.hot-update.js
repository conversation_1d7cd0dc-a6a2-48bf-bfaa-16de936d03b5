"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/YouTubeConverter.tsx":
/*!*********************************************!*\
  !*** ./src/components/YouTubeConverter.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ YouTubeConverter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Download,Music,Settings,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Download,Music,Settings,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Download,Music,Settings,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Download,Music,Settings,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Download,Music,Settings,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Download,Music,Settings,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _ConversionProgress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ConversionProgress */ \"(app-pages-browser)/./src/components/ConversionProgress.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction YouTubeConverter() {\n    _s();\n    const [url, setUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [options, setOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        bitrate: 192,\n        durationFactor: 1.0\n    });\n    const [isConverting, setIsConverting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showOptions, setShowOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProgress, setShowProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleConvert = async ()=>{\n        if (!(0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.isValidYouTubeUrl)(url)) {\n            alert('Please enter a valid YouTube URL. Supported formats:\\n• https://youtube.com/watch?v=...\\n• https://youtube.com/shorts/...\\n• https://youtu.be/...\\n• https://youtube.com/embed/...');\n            return;\n        }\n        setIsConverting(true);\n        setShowProgress(true);\n    };\n    const handleProgressClose = ()=>{\n        setShowProgress(false);\n        setIsConverting(false);\n    };\n    const bitrateOptions = [\n        {\n            value: 128,\n            label: '128 kbps (Good)'\n        },\n        {\n            value: 192,\n            label: '192 kbps (High)'\n        },\n        {\n            value: 320,\n            label: '320 kbps (Best)'\n        }\n    ];\n    const durationOptions = [\n        {\n            value: 1.0,\n            label: '1.0x (Normal)'\n        },\n        {\n            value: 1.5,\n            label: '1.5x (Faster)'\n        },\n        {\n            value: 2.0,\n            label: '2.0x (Fastest)'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: -20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6\n                },\n                className: \"text-center mb-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-12 h-12 text-red-600 mr-3\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-5xl font-bold text-gray-900\",\n                                children: \"YouTube to MP3\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                        children: \"Convert YouTube videos to high-quality MP3 audio files. Fast, free, and easy to use.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6,\n                    delay: 0.2\n                },\n                className: \"bg-white rounded-2xl shadow-2xl p-8 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                className: \"absolute inset-0 rounded-xl bg-gradient-to-r from-red-500 via-yellow-500 via-green-500 via-blue-500 to-purple-500 opacity-0 transition-opacity duration-300 \".concat(isHovered ? 'opacity-100' : 'opacity-0'),\n                                style: {\n                                    background: isHovered ? 'linear-gradient(90deg, #ef4444, #f59e0b, #10b981, #3b82f6, #8b5cf6, #ef4444)' : 'transparent',\n                                    backgroundSize: '200% 100%',\n                                    animation: isHovered ? 'rainbow 2s linear infinite' : 'none'\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative bg-white rounded-xl p-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: url,\n                                    onChange: (e)=>setUrl(e.target.value),\n                                    placeholder: \"Paste YouTube URL here (videos, shorts, etc.)...\",\n                                    className: \"w-full px-6 py-4 text-lg border-2 border-gray-200 rounded-lg focus:border-red-500 focus:outline-none transition-colors\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"Supports: Regular videos, YouTube Shorts, youtu.be links, and embed URLs\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowOptions(!showOptions),\n                            className: \"flex items-center text-gray-600 hover:text-gray-800 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-5 h-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this),\n                                \"Conversion Options\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    animate: {\n                                        rotate: showOptions ? 180 : 0\n                                    },\n                                    transition: {\n                                        duration: 0.2\n                                    },\n                                    className: \"ml-2\",\n                                    children: \"▼\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: false,\n                        animate: {\n                            height: showOptions ? 'auto' : 0,\n                            opacity: showOptions ? 1 : 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        className: \"overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 gap-6 mb-6 p-4 bg-gray-50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Audio Quality\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: options.bitrate,\n                                            onChange: (e)=>setOptions({\n                                                    ...options,\n                                                    bitrate: Number(e.target.value)\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-red-500 focus:outline-none\",\n                                            children: bitrateOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: option.value,\n                                                    children: option.label\n                                                }, option.value, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Playback Speed\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: options.durationFactor,\n                                            onChange: (e)=>setOptions({\n                                                    ...options,\n                                                    durationFactor: Number(e.target.value)\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-red-500 focus:outline-none\",\n                                            children: durationOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: option.value,\n                                                    children: option.label\n                                                }, option.value, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            onClick: handleConvert,\n                            onMouseEnter: ()=>setIsHovered(true),\n                            onMouseLeave: ()=>setIsHovered(false),\n                            isLoading: isConverting,\n                            size: \"lg\",\n                            className: \"px-12 py-4 text-xl\",\n                            disabled: !url.trim(),\n                            children: isConverting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-6 h-6 mr-2 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Converting...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-6 h-6 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Convert to MP3\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6,\n                    delay: 0.4\n                },\n                className: \"grid md:grid-cols-3 gap-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 bg-white rounded-xl shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-8 h-8 text-red-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-2\",\n                                children: \"Lightning Fast\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Convert videos in seconds with our optimized servers\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 bg-white rounded-xl shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-8 h-8 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-2\",\n                                children: \"High Quality\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Multiple bitrate options for the best audio quality\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 bg-white rounded-xl shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-8 h-8 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-2\",\n                                children: \"Free Forever\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"No registration required, completely free to use\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6,\n                    delay: 0.6\n                },\n                className: \"grid md:grid-cols-2 gap-6 mt-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/history\",\n                        className: \"group bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center group-hover:bg-blue-200 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-6 h-6 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold ml-4 text-gray-900\",\n                                        children: \"View History\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Track your conversion history and re-download files\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/popular\",\n                        className: \"group bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center group-hover:bg-orange-200 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-6 h-6 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold ml-4 text-gray-900\",\n                                        children: \"Popular Today\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Discover today's most converted YouTube videos\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConversionProgress__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isVisible: showProgress,\n                onClose: handleProgressClose,\n                videoUrl: url,\n                options: options\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n_s(YouTubeConverter, \"Lh1INM+8edi3CwXn/4Y27WX7S04=\");\n_c = YouTubeConverter;\nvar _c;\n$RefreshReg$(_c, \"YouTubeConverter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/YouTubeConverter.tsx\n"));

/***/ })

});