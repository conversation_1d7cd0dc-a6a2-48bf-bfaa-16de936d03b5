"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   extractVideoId: () => (/* binding */ extractVideoId),\n/* harmony export */   isValidYouTubeUrl: () => (/* binding */ isValidYouTubeUrl)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn() {\n    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){\n        inputs[_key] = arguments[_key];\n    }\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction isValidYouTubeUrl(url) {\n    const youtubeRegex = /^(https?:\\/\\/)?(www\\.)?(youtube\\.com\\/(watch\\?v=|embed\\/|v\\/|shorts\\/)|youtu\\.be\\/)[\\w-]+/;\n    return youtubeRegex.test(url);\n}\nfunction extractVideoId(url) {\n    // Updated regex to handle YouTube Shorts URLs\n    const regex = /(?:youtube\\.com\\/(?:[^\\/]+\\/.+\\/|(?:v|e(?:mbed)?)\\/|shorts\\/|.*[?&]v=)|youtu\\.be\\/)([^\"&?\\/\\s]{11})/;\n    const match = url.match(regex);\n    return match ? match[1] : null;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBNkM7QUFDSjtBQUVsQyxTQUFTRTtJQUFHO1FBQUdDLE9BQUgsdUJBQXVCOztJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCO0FBRU8sU0FBU0Msa0JBQWtCQyxHQUFXO0lBQzNDLE1BQU1DLGVBQWU7SUFDckIsT0FBT0EsYUFBYUMsSUFBSSxDQUFDRjtBQUMzQjtBQUVPLFNBQVNHLGVBQWVILEdBQVc7SUFDeEMsOENBQThDO0lBQzlDLE1BQU1JLFFBQVE7SUFDZCxNQUFNQyxRQUFRTCxJQUFJSyxLQUFLLENBQUNEO0lBQ3hCLE9BQU9DLFFBQVFBLEtBQUssQ0FBQyxFQUFFLEdBQUc7QUFDNUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zdW1pYW4vRGVza3RvcC9pZGVhbC95b3V0dWJlX21wMy93ZWIvc3JjL2xpYi91dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlIENsYXNzVmFsdWUsIGNsc3ggfSBmcm9tICdjbHN4JztcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tICd0YWlsd2luZC1tZXJnZSc7XG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gaXNWYWxpZFlvdVR1YmVVcmwodXJsOiBzdHJpbmcpOiBib29sZWFuIHtcbiAgY29uc3QgeW91dHViZVJlZ2V4ID0gL14oaHR0cHM/OlxcL1xcLyk/KHd3d1xcLik/KHlvdXR1YmVcXC5jb21cXC8od2F0Y2hcXD92PXxlbWJlZFxcL3x2XFwvfHNob3J0c1xcLyl8eW91dHVcXC5iZVxcLylbXFx3LV0rLztcbiAgcmV0dXJuIHlvdXR1YmVSZWdleC50ZXN0KHVybCk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBleHRyYWN0VmlkZW9JZCh1cmw6IHN0cmluZyk6IHN0cmluZyB8IG51bGwge1xuICAvLyBVcGRhdGVkIHJlZ2V4IHRvIGhhbmRsZSBZb3VUdWJlIFNob3J0cyBVUkxzXG4gIGNvbnN0IHJlZ2V4ID0gLyg/OnlvdXR1YmVcXC5jb21cXC8oPzpbXlxcL10rXFwvLitcXC98KD86dnxlKD86bWJlZCk/KVxcL3xzaG9ydHNcXC98LipbPyZddj0pfHlvdXR1XFwuYmVcXC8pKFteXCImP1xcL1xcc117MTF9KS87XG4gIGNvbnN0IG1hdGNoID0gdXJsLm1hdGNoKHJlZ2V4KTtcbiAgcmV0dXJuIG1hdGNoID8gbWF0Y2hbMV0gOiBudWxsO1xufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiLCJpc1ZhbGlkWW91VHViZVVybCIsInVybCIsInlvdXR1YmVSZWdleCIsInRlc3QiLCJleHRyYWN0VmlkZW9JZCIsInJlZ2V4IiwibWF0Y2giXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils.ts\n"));

/***/ })

});