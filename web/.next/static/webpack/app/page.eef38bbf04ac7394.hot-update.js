"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   extractVideoId: () => (/* binding */ extractVideoId),\n/* harmony export */   isValidYouTubeUrl: () => (/* binding */ isValidYouTubeUrl)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn() {\n    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){\n        inputs[_key] = arguments[_key];\n    }\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction isValidYouTubeUrl(url) {\n    // Comprehensive YouTube URL validation including Shorts\n    const youtubePatterns = [\n        /^(https?:\\/\\/)?(www\\.)?youtube\\.com\\/watch\\?v=[\\w-]+/,\n        /^(https?:\\/\\/)?(www\\.)?youtube\\.com\\/shorts\\/[\\w-]+/,\n        /^(https?:\\/\\/)?(www\\.)?youtube\\.com\\/embed\\/[\\w-]+/,\n        /^(https?:\\/\\/)?(www\\.)?youtube\\.com\\/v\\/[\\w-]+/,\n        /^(https?:\\/\\/)?youtu\\.be\\/[\\w-]+/,\n        /^(https?:\\/\\/)?(www\\.)?youtube\\.com\\/watch\\?.*v=[\\w-]+/\n    ];\n    return youtubePatterns.some((pattern)=>pattern.test(url));\n}\nfunction extractVideoId(url) {\n    // Comprehensive video ID extraction for all YouTube URL formats\n    const patterns = [\n        /(?:youtube\\.com\\/watch\\?v=|youtube\\.com\\/embed\\/|youtube\\.com\\/v\\/|youtube\\.com\\/shorts\\/|youtu\\.be\\/)([a-zA-Z0-9_-]{11})/,\n        /youtube\\.com\\/watch\\?.*v=([a-zA-Z0-9_-]{11})/\n    ];\n    for (const pattern of patterns){\n        const match = url.match(pattern);\n        if (match && match[1]) {\n            return match[1];\n        }\n    }\n    return null;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils.ts\n"));

/***/ })

});