"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/circle-alert.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ CircleAlert)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.544.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12\",\n            y1: \"8\",\n            y2: \"12\",\n            key: \"1pkeuh\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12.01\",\n            y1: \"16\",\n            y2: \"16\",\n            key: \"4dfq90\"\n        }\n    ]\n];\nconst CircleAlert = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"circle-alert\", __iconNode);\n //# sourceMappingURL=circle-alert.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js":
/*!**********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/circle-check-big.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ CircleCheckBig)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.544.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M21.801 10A10 10 0 1 1 17 3.335\",\n            key: \"yps3ct\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m9 11 3 3L22 4\",\n            key: \"1pflzl\"\n        }\n    ]\n];\nconst CircleCheckBig = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"circle-check-big\", __iconNode);\n //# sourceMappingURL=circle-check-big.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/loader.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Loader)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.544.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M12 2v4\",\n            key: \"3427ic\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m16.2 7.8 2.9-2.9\",\n            key: \"r700ao\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M18 12h4\",\n            key: \"wj9ykh\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m16.2 16.2 2.9 2.9\",\n            key: \"1bxg5t\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 18v4\",\n            key: \"jadmvz\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m4.9 19.1 2.9-2.9\",\n            key: \"bwix9q\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M2 12h4\",\n            key: \"j09sii\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m4.9 4.9 2.9 2.9\",\n            key: \"giyufr\"\n        }\n    ]\n];\nconst Loader = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"loader\", __iconNode);\n //# sourceMappingURL=loader.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ConversionProgress.tsx":
/*!***********************************************!*\
  !*** ./src/components/ConversionProgress.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ConversionProgress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,Loader!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,Loader!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,Loader!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,Loader!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ConversionProgress(param) {\n    let { isVisible, onClose, videoUrl, options } = param;\n    _s();\n    const [steps, setSteps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 'fetch',\n            label: 'Fetching video information',\n            status: 'pending'\n        },\n        {\n            id: 'download',\n            label: 'Downloading video',\n            status: 'pending',\n            progress: 0\n        },\n        {\n            id: 'convert',\n            label: 'Converting to MP3',\n            status: 'pending',\n            progress: 0\n        },\n        {\n            id: 'upload',\n            label: 'Preparing download',\n            status: 'pending',\n            progress: 0\n        }\n    ]);\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [overallProgress, setOverallProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [estimatedTime, setEstimatedTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('Calculating...');\n    const [downloadUrl, setDownloadUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConversionProgress.useEffect\": ()=>{\n            if (!isVisible) return;\n            // Simulate conversion process\n            const simulateConversion = {\n                \"ConversionProgress.useEffect.simulateConversion\": async ()=>{\n                    try {\n                        // Step 1: Fetch video info\n                        setSteps({\n                            \"ConversionProgress.useEffect.simulateConversion\": (prev)=>prev.map({\n                                    \"ConversionProgress.useEffect.simulateConversion\": (step, index)=>index === 0 ? {\n                                            ...step,\n                                            status: 'processing'\n                                        } : step\n                                }[\"ConversionProgress.useEffect.simulateConversion\"])\n                        }[\"ConversionProgress.useEffect.simulateConversion\"]);\n                        setEstimatedTime('2-3 minutes');\n                        await new Promise({\n                            \"ConversionProgress.useEffect.simulateConversion\": (resolve)=>setTimeout(resolve, 2000)\n                        }[\"ConversionProgress.useEffect.simulateConversion\"]);\n                        setSteps({\n                            \"ConversionProgress.useEffect.simulateConversion\": (prev)=>prev.map({\n                                    \"ConversionProgress.useEffect.simulateConversion\": (step, index)=>index === 0 ? {\n                                            ...step,\n                                            status: 'completed'\n                                        } : step\n                                }[\"ConversionProgress.useEffect.simulateConversion\"])\n                        }[\"ConversionProgress.useEffect.simulateConversion\"]);\n                        setCurrentStep(1);\n                        setOverallProgress(25);\n                        // Step 2: Download video\n                        setSteps({\n                            \"ConversionProgress.useEffect.simulateConversion\": (prev)=>prev.map({\n                                    \"ConversionProgress.useEffect.simulateConversion\": (step, index)=>index === 1 ? {\n                                            ...step,\n                                            status: 'processing'\n                                        } : step\n                                }[\"ConversionProgress.useEffect.simulateConversion\"])\n                        }[\"ConversionProgress.useEffect.simulateConversion\"]);\n                        setEstimatedTime('1-2 minutes');\n                        for(let i = 0; i <= 100; i += 10){\n                            await new Promise({\n                                \"ConversionProgress.useEffect.simulateConversion\": (resolve)=>setTimeout(resolve, 200)\n                            }[\"ConversionProgress.useEffect.simulateConversion\"]);\n                            setSteps({\n                                \"ConversionProgress.useEffect.simulateConversion\": (prev)=>prev.map({\n                                        \"ConversionProgress.useEffect.simulateConversion\": (step, index)=>index === 1 ? {\n                                                ...step,\n                                                progress: i\n                                            } : step\n                                    }[\"ConversionProgress.useEffect.simulateConversion\"])\n                            }[\"ConversionProgress.useEffect.simulateConversion\"]);\n                            setOverallProgress(25 + i * 0.25);\n                        }\n                        setSteps({\n                            \"ConversionProgress.useEffect.simulateConversion\": (prev)=>prev.map({\n                                    \"ConversionProgress.useEffect.simulateConversion\": (step, index)=>index === 1 ? {\n                                            ...step,\n                                            status: 'completed'\n                                        } : step\n                                }[\"ConversionProgress.useEffect.simulateConversion\"])\n                        }[\"ConversionProgress.useEffect.simulateConversion\"]);\n                        setCurrentStep(2);\n                        // Step 3: Convert to MP3\n                        setSteps({\n                            \"ConversionProgress.useEffect.simulateConversion\": (prev)=>prev.map({\n                                    \"ConversionProgress.useEffect.simulateConversion\": (step, index)=>index === 2 ? {\n                                            ...step,\n                                            status: 'processing'\n                                        } : step\n                                }[\"ConversionProgress.useEffect.simulateConversion\"])\n                        }[\"ConversionProgress.useEffect.simulateConversion\"]);\n                        setEstimatedTime('30-60 seconds');\n                        for(let i = 0; i <= 100; i += 15){\n                            await new Promise({\n                                \"ConversionProgress.useEffect.simulateConversion\": (resolve)=>setTimeout(resolve, 150)\n                            }[\"ConversionProgress.useEffect.simulateConversion\"]);\n                            setSteps({\n                                \"ConversionProgress.useEffect.simulateConversion\": (prev)=>prev.map({\n                                        \"ConversionProgress.useEffect.simulateConversion\": (step, index)=>index === 2 ? {\n                                                ...step,\n                                                progress: i\n                                            } : step\n                                    }[\"ConversionProgress.useEffect.simulateConversion\"])\n                            }[\"ConversionProgress.useEffect.simulateConversion\"]);\n                            setOverallProgress(50 + i * 0.25);\n                        }\n                        setSteps({\n                            \"ConversionProgress.useEffect.simulateConversion\": (prev)=>prev.map({\n                                    \"ConversionProgress.useEffect.simulateConversion\": (step, index)=>index === 2 ? {\n                                            ...step,\n                                            status: 'completed'\n                                        } : step\n                                }[\"ConversionProgress.useEffect.simulateConversion\"])\n                        }[\"ConversionProgress.useEffect.simulateConversion\"]);\n                        setCurrentStep(3);\n                        // Step 4: Upload/Prepare\n                        setSteps({\n                            \"ConversionProgress.useEffect.simulateConversion\": (prev)=>prev.map({\n                                    \"ConversionProgress.useEffect.simulateConversion\": (step, index)=>index === 3 ? {\n                                            ...step,\n                                            status: 'processing'\n                                        } : step\n                                }[\"ConversionProgress.useEffect.simulateConversion\"])\n                        }[\"ConversionProgress.useEffect.simulateConversion\"]);\n                        setEstimatedTime('10-20 seconds');\n                        for(let i = 0; i <= 100; i += 20){\n                            await new Promise({\n                                \"ConversionProgress.useEffect.simulateConversion\": (resolve)=>setTimeout(resolve, 100)\n                            }[\"ConversionProgress.useEffect.simulateConversion\"]);\n                            setSteps({\n                                \"ConversionProgress.useEffect.simulateConversion\": (prev)=>prev.map({\n                                        \"ConversionProgress.useEffect.simulateConversion\": (step, index)=>index === 3 ? {\n                                                ...step,\n                                                progress: i\n                                            } : step\n                                    }[\"ConversionProgress.useEffect.simulateConversion\"])\n                            }[\"ConversionProgress.useEffect.simulateConversion\"]);\n                            setOverallProgress(75 + i * 0.25);\n                        }\n                        setSteps({\n                            \"ConversionProgress.useEffect.simulateConversion\": (prev)=>prev.map({\n                                    \"ConversionProgress.useEffect.simulateConversion\": (step, index)=>index === 3 ? {\n                                            ...step,\n                                            status: 'completed'\n                                        } : step\n                                }[\"ConversionProgress.useEffect.simulateConversion\"])\n                        }[\"ConversionProgress.useEffect.simulateConversion\"]);\n                        setOverallProgress(100);\n                        setEstimatedTime('Completed!');\n                        setDownloadUrl('https://example.com/download.mp3'); // Mock download URL\n                    } catch (err) {\n                        setError('Conversion failed. Please try again.');\n                        setSteps({\n                            \"ConversionProgress.useEffect.simulateConversion\": (prev)=>prev.map({\n                                    \"ConversionProgress.useEffect.simulateConversion\": (step, index)=>index === currentStep ? {\n                                            ...step,\n                                            status: 'error'\n                                        } : step\n                                }[\"ConversionProgress.useEffect.simulateConversion\"])\n                        }[\"ConversionProgress.useEffect.simulateConversion\"]);\n                    }\n                }\n            }[\"ConversionProgress.useEffect.simulateConversion\"];\n            simulateConversion();\n        }\n    }[\"ConversionProgress.useEffect\"], [\n        isVisible,\n        currentStep\n    ]);\n    const getStepIcon = (step)=>{\n        switch(step.status){\n            case 'completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-5 h-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 16\n                }, this);\n            case 'processing':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-5 h-5 text-blue-500 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-5 h-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-5 h-5 rounded-full border-2 border-gray-300\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n        initial: {\n            opacity: 0\n        },\n        animate: {\n            opacity: 1\n        },\n        exit: {\n            opacity: 0\n        },\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n            initial: {\n                scale: 0.9,\n                opacity: 0\n            },\n            animate: {\n                scale: 1,\n                opacity: 1\n            },\n            exit: {\n                scale: 0.9,\n                opacity: 0\n            },\n            className: \"bg-white rounded-2xl shadow-2xl max-w-md w-full p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                            children: \"Converting Video\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-sm\",\n                            children: [\n                                \"Quality: \",\n                                options.bitrate,\n                                \"kbps • Speed: \",\n                                options.durationFactor,\n                                \"x\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-gray-700\",\n                                    children: \"Overall Progress\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        Math.round(overallProgress),\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-200 rounded-full h-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full\",\n                                initial: {\n                                    width: 0\n                                },\n                                animate: {\n                                    width: \"\".concat(overallProgress, \"%\")\n                                },\n                                transition: {\n                                    duration: 0.5\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600 mt-2\",\n                            children: [\n                                \"Estimated time: \",\n                                estimatedTime\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 mb-6\",\n                    children: steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                getStepIcon(step),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium \".concat(step.status === 'completed' ? 'text-green-700' : step.status === 'processing' ? 'text-blue-700' : step.status === 'error' ? 'text-red-700' : 'text-gray-500'),\n                                            children: step.label\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 17\n                                        }, this),\n                                        step.status === 'processing' && step.progress !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-gray-200 rounded-full h-1 mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                className: \"bg-blue-500 h-1 rounded-full\",\n                                                initial: {\n                                                    width: 0\n                                                },\n                                                animate: {\n                                                    width: \"\".concat(step.progress, \"%\")\n                                                },\n                                                transition: {\n                                                    duration: 0.3\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, step.id, true, {\n                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-5 h-5 text-red-500 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-700 text-sm\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-3\",\n                    children: downloadUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: downloadUrl,\n                                download: true,\n                                className: \"flex-1 bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors flex items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-5 h-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Download MP3\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors\",\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"w-full py-3 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors\",\n                        disabled: !error && overallProgress < 100,\n                        children: error ? 'Close' : 'Cancel'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n            lineNumber: 154,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, this);\n}\n_s(ConversionProgress, \"JyVelpNPeY6/LudqOMzPl6MB34U=\");\n_c = ConversionProgress;\nvar _c;\n$RefreshReg$(_c, \"ConversionProgress\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ConversionProgress.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/YouTubeConverter.tsx":
/*!*********************************************!*\
  !*** ./src/components/YouTubeConverter.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ YouTubeConverter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Download,Music,Settings,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Download,Music,Settings,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Download,Music,Settings,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Download,Music,Settings,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Download,Music,Settings,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Download,Music,Settings,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _ConversionProgress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ConversionProgress */ \"(app-pages-browser)/./src/components/ConversionProgress.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction YouTubeConverter() {\n    _s();\n    const [url, setUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [options, setOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        bitrate: 192,\n        durationFactor: 1.0\n    });\n    const [isConverting, setIsConverting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showOptions, setShowOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProgress, setShowProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleConvert = async ()=>{\n        if (!(0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.isValidYouTubeUrl)(url)) {\n            alert('Please enter a valid YouTube URL');\n            return;\n        }\n        setIsConverting(true);\n        setShowProgress(true);\n    };\n    const handleProgressClose = ()=>{\n        setShowProgress(false);\n        setIsConverting(false);\n    };\n    const bitrateOptions = [\n        {\n            value: 128,\n            label: '128 kbps (Good)'\n        },\n        {\n            value: 192,\n            label: '192 kbps (High)'\n        },\n        {\n            value: 320,\n            label: '320 kbps (Best)'\n        }\n    ];\n    const durationOptions = [\n        {\n            value: 1.0,\n            label: '1.0x (Normal)'\n        },\n        {\n            value: 1.5,\n            label: '1.5x (Faster)'\n        },\n        {\n            value: 2.0,\n            label: '2.0x (Fastest)'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: -20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6\n                },\n                className: \"text-center mb-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-12 h-12 text-red-600 mr-3\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-5xl font-bold text-gray-900\",\n                                children: \"YouTube to MP3\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                        children: \"Convert YouTube videos to high-quality MP3 audio files. Fast, free, and easy to use.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6,\n                    delay: 0.2\n                },\n                className: \"bg-white rounded-2xl shadow-2xl p-8 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                className: \"absolute inset-0 rounded-xl bg-gradient-to-r from-red-500 via-yellow-500 via-green-500 via-blue-500 to-purple-500 opacity-0 transition-opacity duration-300 \".concat(isHovered ? 'opacity-100' : 'opacity-0'),\n                                style: {\n                                    background: isHovered ? 'linear-gradient(90deg, #ef4444, #f59e0b, #10b981, #3b82f6, #8b5cf6, #ef4444)' : 'transparent',\n                                    backgroundSize: '200% 100%',\n                                    animation: isHovered ? 'rainbow 2s linear infinite' : 'none'\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative bg-white rounded-xl p-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: url,\n                                    onChange: (e)=>setUrl(e.target.value),\n                                    placeholder: \"Paste YouTube URL here...\",\n                                    className: \"w-full px-6 py-4 text-lg border-2 border-gray-200 rounded-lg focus:border-red-500 focus:outline-none transition-colors\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowOptions(!showOptions),\n                            className: \"flex items-center text-gray-600 hover:text-gray-800 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-5 h-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                \"Conversion Options\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    animate: {\n                                        rotate: showOptions ? 180 : 0\n                                    },\n                                    transition: {\n                                        duration: 0.2\n                                    },\n                                    className: \"ml-2\",\n                                    children: \"▼\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: false,\n                        animate: {\n                            height: showOptions ? 'auto' : 0,\n                            opacity: showOptions ? 1 : 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        className: \"overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 gap-6 mb-6 p-4 bg-gray-50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Audio Quality\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: options.bitrate,\n                                            onChange: (e)=>setOptions({\n                                                    ...options,\n                                                    bitrate: Number(e.target.value)\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-red-500 focus:outline-none\",\n                                            children: bitrateOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: option.value,\n                                                    children: option.label\n                                                }, option.value, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Playback Speed\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: options.durationFactor,\n                                            onChange: (e)=>setOptions({\n                                                    ...options,\n                                                    durationFactor: Number(e.target.value)\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-red-500 focus:outline-none\",\n                                            children: durationOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: option.value,\n                                                    children: option.label\n                                                }, option.value, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            onClick: handleConvert,\n                            onMouseEnter: ()=>setIsHovered(true),\n                            onMouseLeave: ()=>setIsHovered(false),\n                            isLoading: isConverting,\n                            size: \"lg\",\n                            className: \"px-12 py-4 text-xl\",\n                            disabled: !url.trim(),\n                            children: isConverting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-6 h-6 mr-2 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Converting...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-6 h-6 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Convert to MP3\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6,\n                    delay: 0.4\n                },\n                className: \"grid md:grid-cols-3 gap-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 bg-white rounded-xl shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-8 h-8 text-red-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-2\",\n                                children: \"Lightning Fast\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Convert videos in seconds with our optimized servers\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 bg-white rounded-xl shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-8 h-8 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-2\",\n                                children: \"High Quality\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Multiple bitrate options for the best audio quality\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 bg-white rounded-xl shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-8 h-8 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-2\",\n                                children: \"Free Forever\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"No registration required, completely free to use\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6,\n                    delay: 0.6\n                },\n                className: \"grid md:grid-cols-2 gap-6 mt-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/history\",\n                        className: \"group bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center group-hover:bg-blue-200 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-6 h-6 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold ml-4 text-gray-900\",\n                                        children: \"View History\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Track your conversion history and re-download files\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/popular\",\n                        className: \"group bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center group-hover:bg-orange-200 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-6 h-6 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold ml-4 text-gray-900\",\n                                        children: \"Popular Today\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Discover today's most converted YouTube videos\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                lineNumber: 230,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConversionProgress__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isVisible: showProgress,\n                onClose: handleProgressClose,\n                videoUrl: url,\n                options: options\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n_s(YouTubeConverter, \"Lh1INM+8edi3CwXn/4Y27WX7S04=\");\n_c = YouTubeConverter;\nvar _c;\n$RefreshReg$(_c, \"YouTubeConverter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/YouTubeConverter.tsx\n"));

/***/ })

});