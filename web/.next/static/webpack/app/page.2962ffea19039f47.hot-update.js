"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/YouTubeConverter.tsx":
/*!*********************************************!*\
  !*** ./src/components/YouTubeConverter.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ YouTubeConverter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Download,Music,Settings,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Download,Music,Settings,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Download,Music,Settings,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Download,Music,Settings,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Download,Music,Settings,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Download,Music,Settings,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction YouTubeConverter() {\n    _s();\n    const [url, setUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [options, setOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        bitrate: 192,\n        durationFactor: 1.0\n    });\n    const [isConverting, setIsConverting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showOptions, setShowOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleConvert = async ()=>{\n        if (!(0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.isValidYouTubeUrl)(url)) {\n            alert('Please enter a valid YouTube URL');\n            return;\n        }\n        setIsConverting(true);\n        // TODO: Implement actual conversion logic\n        setTimeout(()=>{\n            setIsConverting(false);\n            alert('Conversion completed! (This is a demo)');\n        }, 3000);\n    };\n    const bitrateOptions = [\n        {\n            value: 128,\n            label: '128 kbps (Good)'\n        },\n        {\n            value: 192,\n            label: '192 kbps (High)'\n        },\n        {\n            value: 320,\n            label: '320 kbps (Best)'\n        }\n    ];\n    const durationOptions = [\n        {\n            value: 1.0,\n            label: '1.0x (Normal)'\n        },\n        {\n            value: 1.5,\n            label: '1.5x (Faster)'\n        },\n        {\n            value: 2.0,\n            label: '2.0x (Fastest)'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: -20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6\n                },\n                className: \"text-center mb-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-12 h-12 text-red-600 mr-3\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-5xl font-bold text-gray-900\",\n                                children: \"YouTube to MP3\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                        children: \"Convert YouTube videos to high-quality MP3 audio files. Fast, free, and easy to use.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6,\n                    delay: 0.2\n                },\n                className: \"bg-white rounded-2xl shadow-2xl p-8 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"absolute inset-0 rounded-xl bg-gradient-to-r from-red-500 via-yellow-500 via-green-500 via-blue-500 to-purple-500 opacity-0 transition-opacity duration-300 \".concat(isHovered ? 'opacity-100' : 'opacity-0'),\n                                style: {\n                                    background: isHovered ? 'linear-gradient(90deg, #ef4444, #f59e0b, #10b981, #3b82f6, #8b5cf6, #ef4444)' : 'transparent',\n                                    backgroundSize: '200% 100%',\n                                    animation: isHovered ? 'rainbow 2s linear infinite' : 'none'\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative bg-white rounded-xl p-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: url,\n                                    onChange: (e)=>setUrl(e.target.value),\n                                    placeholder: \"Paste YouTube URL here...\",\n                                    className: \"w-full px-6 py-4 text-lg border-2 border-gray-200 rounded-lg focus:border-red-500 focus:outline-none transition-colors\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowOptions(!showOptions),\n                            className: \"flex items-center text-gray-600 hover:text-gray-800 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-5 h-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this),\n                                \"Conversion Options\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    animate: {\n                                        rotate: showOptions ? 180 : 0\n                                    },\n                                    transition: {\n                                        duration: 0.2\n                                    },\n                                    className: \"ml-2\",\n                                    children: \"▼\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: false,\n                        animate: {\n                            height: showOptions ? 'auto' : 0,\n                            opacity: showOptions ? 1 : 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        className: \"overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 gap-6 mb-6 p-4 bg-gray-50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Audio Quality\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: options.bitrate,\n                                            onChange: (e)=>setOptions({\n                                                    ...options,\n                                                    bitrate: Number(e.target.value)\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-red-500 focus:outline-none\",\n                                            children: bitrateOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: option.value,\n                                                    children: option.label\n                                                }, option.value, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Playback Speed\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: options.durationFactor,\n                                            onChange: (e)=>setOptions({\n                                                    ...options,\n                                                    durationFactor: Number(e.target.value)\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-red-500 focus:outline-none\",\n                                            children: durationOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: option.value,\n                                                    children: option.label\n                                                }, option.value, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            onClick: handleConvert,\n                            onMouseEnter: ()=>setIsHovered(true),\n                            onMouseLeave: ()=>setIsHovered(false),\n                            isLoading: isConverting,\n                            size: \"lg\",\n                            className: \"px-12 py-4 text-xl\",\n                            disabled: !url.trim(),\n                            children: isConverting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-6 h-6 mr-2 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Converting...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-6 h-6 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Convert to MP3\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6,\n                    delay: 0.4\n                },\n                className: \"grid md:grid-cols-3 gap-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 bg-white rounded-xl shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-8 h-8 text-red-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-2\",\n                                children: \"Lightning Fast\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Convert videos in seconds with our optimized servers\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 bg-white rounded-xl shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-8 h-8 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-2\",\n                                children: \"High Quality\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Multiple bitrate options for the best audio quality\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 bg-white rounded-xl shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-8 h-8 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-2\",\n                                children: \"Free Forever\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"No registration required, completely free to use\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6,\n                    delay: 0.6\n                },\n                className: \"grid md:grid-cols-2 gap-6 mt-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/history\",\n                        className: \"group bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center group-hover:bg-blue-200 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-6 h-6 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold ml-4 text-gray-900\",\n                                        children: \"View History\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Track your conversion history and re-download files\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/popular\",\n                        className: \"group bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center group-hover:bg-orange-200 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Music_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-6 h-6 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold ml-4 text-gray-900\",\n                                        children: \"Popular Today\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Discover today's most converted YouTube videos\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n_s(YouTubeConverter, \"EUlKg8I+iMSfpNN2lKTNhHzLJ80=\");\n_c = YouTubeConverter;\nvar _c;\n$RefreshReg$(_c, \"YouTubeConverter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/YouTubeConverter.tsx\n"));

/***/ })

});