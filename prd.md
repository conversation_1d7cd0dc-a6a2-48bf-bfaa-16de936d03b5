好的，这是一个为“YouTube to MP3”Web网站和浏览器插件设计的PRD（产品需求文档）。

## 产品需求文档：YouTube to MP3 转换器

**产品名称:** YouTube to MP3 Converter
**版本:** 1.0
**日期:** 2023年10月27日
**作者:** AI Assistant

### 1. 概述

本项目旨在开发一个Web应用程序和一个浏览器插件，允许用户输入YouTube视频URL，并将其转换为MP3格式进行下载。该产品将提供灵活的转换选项，包括比特率选择和时长调整，并提供友好的用户界面和实时的转换进度反馈。

### 2. 目标

*   提供一个稳定、高效的YouTube视频到MP3音频的转换服务。
*   支持多种比特率选项，满足用户对音质的需求。
*   提供音频时长调整功能，增加用户自定义的灵活性。
*   通过Web网站和浏览器插件（Firefox, Edge）覆盖更广泛的用户群体。
*   提供良好的用户体验，包括实时进度显示和视觉效果。
*   建立用户下载历史和热门下载列表，提升社区感和使用便利性。

### 3. 用户故事

*   作为一个用户，我希望能够输入YouTube视频链接，然后下载转换后的MP3文件。
*   作为一个用户，我希望在转换前可以选择MP3的比特率，以获取不同音质的音频。
*   作为一个用户，我希望能够调整转换后MP3的播放时长（例如1x, 1.5x, 2.0x），以适应我的播放需求。
*   作为一个用户，我希望在转换过程中看到转换进度和预计所需时间。
*   作为一个用户，我希望能够查看我的转换历史记录。
*   作为一个用户，我希望能够看到当前热门的MP3下载。
*   作为一个用户，我希望在Firefox或Edge浏览器中可以直接通过插件转换YouTube视频。
*   作为一个用户，我希望在鼠标悬停在“转换”按钮时，能看到一个酷炫的视觉效果。

### 4. 功能需求

#### 4.1. Web网站核心功能

##### 4.1.1. URL输入与提交
*   **功能描述:** 用户在主页上输入YouTube视频URL。
*   **UI元素:** 一个文本输入框 (`<input type="text">`) 和一个“转换”按钮 (`<button>`)。
*   **交互:**
    *   用户将YouTube视频URL粘贴到输入框。
    *   点击“转换”按钮触发转换过程。
    *   **彩蛋:** 鼠标悬停在“转换”按钮时，文本输入框四周出现彩色跑马灯效果。

##### 4.1.2. 转换选项
*   **功能描述:** 用户在提交URL后或提交前选择MP3的比特率和调整时长。
*   **UI元素:**
    *   **比特率选择:** 下拉菜单或单选按钮组（例如：128kbps, 192kbps, 320kbps）。
    *   **时长调整:** 下拉菜单或单选按钮组（例如：1.0x, 1.5x, 2.0x）。
*   **默认值:** 默认比特率设置为192kbps，时长调整为1.0x。

##### 4.1.3. 转换进度显示
*   **功能描述:** 在转换过程中，实时显示进度条和预计所需时间。
*   **UI元素:** 进度条 (`<progress>`), 文本显示（例如：“正在转换：50%”, “预计剩余：30秒”）。
*   **交互:** 进度条动态更新，文本信息实时刷新。

##### 4.1.4. MP3下载
*   **功能描述:** 转换完成后，提供MP3文件的下载链接。
*   **UI元素:** 下载按钮 (`<button>`) 或自动触发下载。
*   **交互:** 用户点击下载按钮，MP3文件开始下载到本地。

##### 4.1.5. 下载历史
*   **功能描述:** 显示用户最近的转换和下载历史记录。
*   **UI元素:** 列表展示，包含转换时间、转换状态（成功/失败）和视频标题/链接。
*   **显示格式:** 例如：“15分钟前 成功转换 [视频标题]”。
*   **存储:** 用户登录后，历史记录与用户关联。未登录用户可显示本地存储的少量历史。

##### 4.1.6. 今日下载Top 10
*   **功能描述:** 显示当天转换和下载次数最多的10个YouTube视频的列表。
*   **UI元素:** 列表展示，包含视频标题和/或下载次数。
*   **交互:** 点击列表项可直接进行转换或查看原视频。

#### 4.2. 浏览器插件功能 (Firefox & Edge)

*   **功能描述:** 提供在YouTube视频页面一键转换并下载MP3的功能。
*   **UI元素:**
    *   在YouTube视频页面，视频播放器下方或旁边添加一个“Convert to MP3”按钮。
    *   点击按钮后，弹出小型转换选项界面（比特率，时长）。
    *   弹出界面中显示转换进度。
    *   转换完成后，提供下载链接。
*   **交互:**
    *   用户浏览YouTube视频时，点击插件图标或视频页面上的按钮。
    *   选择转换选项后，触发后端转换。
    *   转换完成后，下载MP3文件。

### 5. 技术架构与技术栈

*   **前端:** Next.js (React框架)
    *   负责Web网站的UI渲染和用户交互。
    *   SSR/SSG优化SEO和加载速度。
*   **后端:**
    *   **数据库:** Supabase (PostgreSQL数据库即服务，包含认证、实时数据库、存储等)
        *   存储用户数据、转换历史、热门下载统计等。
    *   **存储:** AWS S3 (用于存储转换后的MP3文件)
    *   **转换服务:** AWS Lambda (无服务器计算服务)
        *   接收YouTube URL，调用`youtube-dl`或其他视频下载库。
        *   将下载的视频文件通过FFmpeg转换为MP3。
        *   将MP3文件上传至AWS S3。
        *   通过API Gateway暴露接口供前端调用。
    *   **API:** RESTful API (Next.js API Routes 或独立API服务)
        *   前端与Lambda、Supabase、S3之间的通信接口。

### 6. 数据模型 (Supabase)

*   **Users 表:**
    *   `id` (UUID, Primary Key)
    *   `email` (Text, Unique)
    *   `created_at` (Timestamp)
    *   ... (Supabase自带的认证字段)
*   **Conversions 表:**
    *   `id` (UUID, Primary Key)
    *   `user_id` (UUID, Foreign Key to Users.id, 可为空)
    *   `youtube_url` (Text)
    *   `video_title` (Text)
    *   `mp3_url` (Text, AWS S3链接)
    *   `bitrate` (Integer)
    *   `duration_factor` (Float)
    *   `status` (Enum: 'pending', 'processing', 'completed', 'failed')
    *   `created_at` (Timestamp)
    *   `completed_at` (Timestamp, 可为空)
*   **Popular_Downloads 表:**
    *   `id` (UUID, Primary Key)
    *   `youtube_url` (Text, Unique)
    *   `video_title` (Text)
    *   `download_count` (Integer, Default 0)
    *   `last_updated` (Timestamp)
    *   `daily_count` (Integer, 每日重置的计数器)

### 7. 性能与可扩展性

*   **转换速度:** 优化Lambda函数，利用并行处理，减少转换时间。
*   **并发处理:** Lambda的自动扩缩容特性将支持高并发转换请求。
*   **存储:** AWS S3提供高可用性和可扩展的存储能力。
*   **数据库:** Supabase（基于PostgreSQL）提供良好的性能和可扩展性。
*   **前端:** Next.js的优化，如图片优化、代码分割等，确保快速加载和流畅的用户体验。

### 8. 安全性

*   **用户认证:** 通过Supabase Auth进行用户注册和登录。
*   **API安全:** 所有的API请求应进行认证和授权。
*   **数据传输:** 使用HTTPS加密所有数据传输。
*   **S3访问:** 对S3桶设置严格的访问策略，仅允许Lambda函数写入和用户下载。
*   **输入校验:** 对用户输入的YouTube URL进行严格的校验，防止恶意输入。

### 9. 监测与日志

*   **Lambda日志:** 使用AWS CloudWatch Logs监测Lambda函数的执行状态和错误。
*   **前端错误:** 使用Sentry或其他错误监控工具捕获前端错误。
*   **转换状态:** 记录每个转换请求的状态，便于故障排查和用户反馈。

### 10. 未来迭代考虑 (MVP之后)

*   支持更多音频格式（例如：WAV, FLAC）。
*   支持视频下载（YouTube to MP4）。
*   批量转换功能。
*   更丰富的时长调整选项（例如：自定义起始/结束时间）。
*   用户账户管理和订阅计划（例如：去除广告、更快转换）。
*   多语言支持。
*   PWA支持。

### 11. UI/UX 设计草图 (高层级描述)

*   **主页:** 简洁明了的输入框和“转换”按钮，周围留有足够的空白。
*   **转换中:** 动画加载指示器或进度条，并显示预计时间。
*   **下载历史/Top 10:** 清晰的列表布局，易于阅读。
*   **按钮效果:** “转换”按钮的跑马灯效果应流畅且不分散注意力，可能使用CSS动画或SVG动画实现。

---

希望这份PRD能够为你的开发团队提供清晰的指导！ 

