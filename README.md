# YouTube to MP3 Converter

A modern web application for converting YouTube videos to MP3 audio files, built with Next.js and designed for deployment on Vercel.

## 🚀 Features Implemented (Phase 1)

### ✅ Web Application Core Features
- **Modern UI/UX**: Clean, responsive design with Tailwind CSS
- **URL Input & Validation**: Comprehensive YouTube URL validation with visual feedback
  - Supports regular videos (`youtube.com/watch?v=...`)
  - Supports YouTube Shorts (`youtube.com/shorts/...`)
  - Supports short URLs (`youtu.be/...`)
  - Supports embed URLs (`youtube.com/embed/...`)
- **Conversion Options**:
  - Bitrate selection (128kbps, 192kbps, 320kbps)
  - Playback speed adjustment (1.0x, 1.5x, 2.0x)
- **Rainbow Border Effect**: Animated colorful border on hover (as per PRD requirement)
- **Real-time Progress Display**: Modal with step-by-step conversion progress
- **Toast Notifications**: Beautiful error/success messages instead of alerts
- **Navigation System**: Clean navigation between pages
- **Responsive Design**: Works on desktop and mobile devices

### ✅ Pages Implemented
1. **Home Page** (`/`): Main converter interface
2. **History Page** (`/history`): User conversion history (mock data)
3. **Popular Page** (`/popular`): Today's top 10 most converted videos (mock data)

### ✅ Components Created
- `YouTubeConverter`: Main conversion interface
- `ConversionProgress`: Real-time progress modal
- `Navigation`: Site navigation
- `Button`: Reusable button component
- Utility functions for URL validation

### ✅ Technical Stack
- **Frontend**: Next.js 14 (App Router), React 18, TypeScript
- **Styling**: Tailwind CSS
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **Deployment Ready**: Configured for Vercel

## 🎯 Current Status

**Phase 1 Complete**: Basic web application with UI/UX and mock functionality

## 🔄 Next Steps (Phase 2)

### Backend Integration
- [ ] AWS Lambda conversion service
- [ ] YouTube video download (yt-dlp)
- [ ] FFmpeg audio conversion
- [ ] AWS S3 file storage
- [ ] Real-time progress via WebSocket

### Database & Auth
- [ ] Supabase setup
- [ ] User authentication
- [ ] Conversion history storage
- [ ] Popular downloads tracking

### Browser Extension
- [ ] Firefox extension development
- [ ] Edge extension development
- [ ] YouTube page integration

## 🛠️ Development

### Prerequisites
- Node.js 18+
- npm or yarn

### Getting Started

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd youtube_mp3
   ```

2. **Install dependencies**
   ```bash
   cd web
   npm install
   ```

3. **Run development server**
   ```bash
   npm run dev
   ```

4. **Open in browser**
   ```
   http://localhost:3000
   ```

5. **Test the application**
   - Test various YouTube URL formats:
     - `https://www.youtube.com/watch?v=dQw4w9WgXcQ` (regular video)
     - `https://www.youtube.com/shorts/Tx387XYIKZg` (YouTube Shorts)
     - `https://youtu.be/dQw4w9WgXcQ` (short URL)
   - Hover over the "Convert" button to see the rainbow effect
   - Click convert to see the progress simulation
   - Navigate to History and Popular pages
   - Test responsive design on different screen sizes
   - Test error handling with invalid URLs

### Project Structure
```
youtube_mp3/
├── web/                    # Next.js web application
│   ├── src/
│   │   ├── app/           # App router pages
│   │   ├── components/    # React components
│   │   └── lib/          # Utility functions
├── lambda/                # AWS Lambda functions (planned)
├── browser-extension/     # Browser extensions (planned)
└── docs/                  # Documentation
```

## 🎨 Design Features

### Rainbow Border Effect
- Animated colorful border appears when hovering over the "Convert" button
- Smooth CSS animations with gradient transitions
- Matches PRD requirements for visual effects

### Progress Display
- Step-by-step conversion progress
- Real-time percentage updates
- Estimated time remaining
- Error handling and retry options

### Responsive Design
- Mobile-first approach
- Adaptive layouts for all screen sizes
- Touch-friendly interface

## 📱 Screenshots

The application features:
- Clean, modern interface
- Intuitive conversion workflow
- Real-time progress feedback
- Easy navigation between features

## 🚀 Deployment

Ready for deployment to Vercel:
```bash
npm run build
```

The application is configured for automatic deployment when connected to a Git repository.

## 📋 TODO

- [ ] Implement actual YouTube video conversion
- [ ] Add user authentication
- [ ] Create browser extensions
- [ ] Set up monitoring and analytics
- [ ] Add more audio format options
- [ ] Implement batch conversion

## 🤝 Contributing

This project follows the PRD specifications for a YouTube to MP3 converter with modern web technologies and cloud deployment.
